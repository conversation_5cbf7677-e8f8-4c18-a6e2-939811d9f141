import * as React from "react";
import "../../styles/public/tailwind.css";

// Simple className utility function
const cn = (...classes: (string | undefined | null | false)[]): string => {
	return classes.filter(Boolean).join(" ");
};

// Logo variant configurations
const logoVariants = {
	variant: {
		color: "",
		"black-gray": "",
		"white-darkmode": "",
		"black-gray-darkmode": "",
	},
	size: {
		sm: "h-[7.5rem] w-8", // 32px width, maintaining aspect ratio
		md: "h-[15rem] w-16", // 64px width (original size)
		lg: "h-[22.5rem] w-24", // 96px width
		xl: "h-[30rem] w-32", // 128px width
	},
};

// Helper function to get logo classes
const getLogoClasses = (
	variant: keyof typeof logoVariants.variant = "color",
	size: keyof typeof logoVariants.size = "md",
	className?: string,
): string => {
	return cn(
		"inline-block",
		logoVariants.variant[variant],
		logoVariants.size[size],
		className,
	);
};

export interface LogoProps extends React.SVGAttributes<SVGSVGElement> {
	/**
	 * Logo variant
	 */
	variant?: keyof typeof logoVariants.variant;
	/**
	 * Logo size
	 */
	size?: keyof typeof logoVariants.size;
	/**
	 * Alternative text for accessibility
	 */
	alt?: string;
}

const RsdsLogo = React.forwardRef<SVGSVGElement, LogoProps>(
	(
		{
			className,
			variant = "color",
			size = "md",
			alt = "RS Design System Logo",
			...props
		},
		ref,
	) => {
		const logoClass = getLogoClasses(variant, size, className);
		const uniqueId = React.useId();

		// Define colors based on variant using Tailwind CSS custom properties
		const colors = {
			color: {
				// Using semantic logo color variables that adapt to theme
				primary: "var(--color-ct-logo-secondar)", // Yellow in light mode, white in dark mode
				secondary: "var(--color-ct-logo-primary)" // Red in light mode, white in dark mode
			},
			"black-gray": {
				primary: "var(--color-ct-logo-black-gray-primary)", // Automatically adapts to dark mode
				secondary: "var(--color-ct-logo-black-gray-secondary)"
			},
			"white-darkmode": {
				primary: "var(--color-cp-white)",
				secondary: "var(--color-cp-white)"
			},
			"black-gray-darkmode": {
				primary: "rgba(255, 255, 255, 0.5)", // Using rgba for opacity since no matching CSS custom property exists
				secondary: "var(--color-cp-white)"
			},
		};

		const { primary, secondary } = colors[variant || "color"];

		// Generate unique IDs for SVG elements
		const mask0Id = `mask0_${uniqueId}`;
		const mask1Id = `mask1_${uniqueId}`;

		return (
			<svg
				ref={ref}
				width="64"
				height="60"
				viewBox="0 0 64 60"
				fill="none"
				xmlns="http://www.w3.org/2000/svg"
				className={logoClass}
				role="img"
				aria-label={alt}
				{...props}
			>
				<g clipPath={`url(#${clipPathId})`}>
					<mask
						id={mask0Id}
						style={{ maskType: "luminance" }}
						maskUnits="userSpaceOnUse"
						x="0"
						y="0"
						width="64"
						height="60"
					>
						<path d="M64 0H0V59.1783H64V0Z" fill="white" />
					</mask>
					<g mask={`url(#${mask0Id})`}>
						<mask
							id={mask1Id}
							style={{ maskType: "luminance" }}
							maskUnits="userSpaceOnUse"
							x="0"
							y="0"
							width="64"
							height="60"
						>
							<path d="M64 0H0V59.1783H64V0Z" fill="white" />
						</mask>
						<g mask={`url(#${mask1Id})`}>
							{/* Primary colored paths */}
							<path
								d="M28.2086 16.9813C29.3407 16.4919 30.5305 16.1227 31.7165 15.8714L31.662 0C27.5631 0.0570656 23.7384 1.24364 20.4844 3.26454L27.5638 17.2791C27.7756 17.1787 27.9862 17.0777 28.2086 16.9813Z"
								fill={primary}
							/>
							<path
								d="M25.9503 18.156L16.8395 6.10535C13.9462 8.89631 11.821 12.4764 10.8154 16.5031L22.9625 21.1569C23.5594 19.9959 24.5866 19.0087 25.9503 18.1567"
								fill={primary}
							/>
							<path
								d="M41.2893 18.3587C44.0428 24.0036 37.7092 23.8658 40.3257 28.2717C41.5411 30.3182 45.3711 32.9884 49.1236 35.2619C52.0379 31.5546 53.7807 26.8824 53.7807 21.801C53.7807 17.3636 52.4518 13.2378 50.1751 9.79358L40.6904 17.4843C40.9226 17.7506 41.1318 18.0353 41.2893 18.3593"
								fill={primary}
							/>
							<path
								d="M39.2984 16.3929L47.1774 6.17422C44.2907 3.36489 40.6306 1.34792 36.5442 0.476196L33.5276 15.5855C35.7249 15.3605 37.7898 15.5986 39.2984 16.3929Z"
								fill={primary}
							/>

							{/* Secondary colored paths */}
							<path
								d="M30.2972 41.5621C28.9893 41.5621 27.9293 40.5021 27.9293 39.1942C27.9293 37.8863 28.9893 36.8263 30.2972 36.8263C31.6051 36.8263 32.6651 37.8863 32.6651 39.1942C32.6651 40.5021 31.6051 41.5621 30.2972 41.5621ZM36.0569 34.3298C31.6511 30.3366 26.2816 33.2285 23.5274 27.8585C22.5494 25.9517 22.2234 24.3119 22.4215 22.8865L10.1741 21.3799C10.1714 21.5203 10.1636 21.6593 10.1636 21.8003C10.1636 33.8444 19.9277 43.6086 31.9718 43.6086C35.3098 43.6086 38.4707 42.8562 41.2991 41.5155C39.8508 38.92 38.0083 36.0982 36.0562 34.3298"
								fill={secondary}
							/>
							<path
								d="M10.0048 48.8678C7.99108 47.9849 6.79139 47.6445 5.61203 47.6445C4.37168 47.6445 3.57866 48.0859 3.57866 48.7674C3.57866 50.8336 10.6358 50.2518 10.6358 55.0263C10.6358 57.6539 8.39841 59.1783 5.30703 59.1783C2.88732 59.1783 1.68763 58.5565 0.30428 57.854V54.9056C2.29764 56.2096 3.51766 56.6707 5.04269 56.6707C6.36438 56.6707 7.07671 56.2096 7.07671 55.4067C7.07671 53.1602 0.0202637 53.9827 0.0202637 49.0679C0.0202637 46.7013 2.0943 45.1362 5.30768 45.1362C6.8537 45.1362 8.25672 45.457 10.0054 46.1995V48.8671"
								fill={secondary}
							/>
							<path
								d="M19.3996 51.7362L25.5213 58.9979H21.1896L15.6988 52.579V58.9979H12.2415V45.3173H15.6988V51.5158H15.7395L20.1119 45.3173H23.9143L19.3996 51.7362Z"
								fill={secondary}
							/>
							<path
								d="M51.3182 58.9979H48.4098L42.3019 50.2321V58.9979H38.8445V45.3173H42.0172L47.8608 53.6823V45.3173H51.3182V58.9979Z"
								fill={secondary}
							/>
							<path
								d="M64 58.9979H53.5681V45.3173H63.8576V47.8249H57.0255V50.6932H63.5526V53.2008H57.0255V56.4903H64V58.9979Z"
								fill={secondary}
							/>
							<path
								d="M30.2979 48.5844L28.2639 53.9013H32.5359L30.2979 48.5844ZM38.4137 58.9972H34.6913L33.552 56.2883H27.3482L26.3105 58.9972H22.6492L28.2429 45.314H32.25L38.413 58.9972"
								fill={secondary}
							/>
						</g>
					</g>
				</g>
			</svg>
		);
	},
);
RsdsLogo.displayName = "Logo";

export { RsdsLogo };
export default RsdsLogo;
