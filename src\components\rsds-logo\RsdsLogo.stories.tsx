import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import RsdsLogo from "./RsdsLogo";

const meta: Meta<typeof RsdsLogo> = {
	title: "Other/RsdsLogo",
	component: RsdsLogo,
	parameters: {
		layout: "centered",
	},
	tags: ["autodocs"],
	argTypes: {
		variant: {
			control: { type: "select" },
			options: ["color", "black-gray", "white-darkmode", "black-gray-darkmode"],
		},
		size: {
			control: { type: "select" },
			options: ["sm", "md", "lg", "xl"],
		},
	},
};

export default meta;
type Story = StoryObj<typeof RsdsLogo>;

export const Default: Story = {
	args: {
		variant: "color",
		size: "md",
		alt: "Region Skåne logo",
	},
};
export const BlackAndGray: Story = {
	args: {
		variant: "black-gray",
		size: "md",
		alt: "Region Skåne logo",
	},
};

export const AllVariants: Story = {
	render: () => (
		<div className="grid grid-cols-2 gap-8 p-8">
			<div className="flex flex-col items-center gap-4 rounded-lg bg-white p-6 border">
				<RsdsLogo variant="color" size="md" />
				<p className="font-medium text-sm">Color (Default)</p>
				<p className="text-gray-500 text-xs">Should show yellow and red</p>
				<div className="flex gap-2 text-xs">
					<div
						className="w-4 h-4 rounded"
						style={{ backgroundColor: "var(--color-cp-yellow)" }}
					/>
					<div
						className="w-4 h-4 rounded"
						style={{ backgroundColor: "var(--color-cp-red)" }}
					/>
				</div>
			</div>

			<div className="flex flex-col items-center gap-4 rounded-lg bg-white p-6 border">
				<RsdsLogo variant="black-gray" size="md" />
				<p className="font-medium text-sm">Black & Gray</p>
				<p className="text-gray-500 text-xs">For light backgrounds</p>
			</div>

			<div className="flex flex-col items-center gap-4 rounded-lg bg-gray-900 p-6">
				<RsdsLogo variant="white-darkmode" size="md" />
				<p className="font-medium text-sm text-white">White (Dark Mode)</p>
				<p className="text-gray-400 text-xs">For dark backgrounds</p>
			</div>

			<div className="flex flex-col items-center gap-4 rounded-lg bg-gray-900 p-6">
				<RsdsLogo variant="black-gray-darkmode" size="md" />
				<p className="font-medium text-sm text-white">
					Black & Gray (Dark Mode)
				</p>
				<p className="text-gray-400 text-xs">For dark backgrounds</p>
			</div>
		</div>
	),
};

export const AllSizes: Story = {
	render: () => (
		<div className="flex items-end gap-8 p-8">
			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="sm" />
				<p className="text-xs">Small</p>
			</div>

			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="md" />
				<p className="text-xs">Medium</p>
			</div>

			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="lg" />
				<p className="text-xs">Large</p>
			</div>

			<div className="flex flex-col items-center gap-2">
				<RsdsLogo variant="color" size="xl" />
				<p className="text-xs">Extra Large</p>
			</div>
		</div>
	),
};

export const CustomStyling: Story = {
	render: () => (
		<div className="flex gap-8 p-8">
			<RsdsLogo
				variant="color"
				size="md"
				className="cursor-pointer opacity-75 transition-opacity hover:opacity-100"
			/>
			<RsdsLogo variant="black-gray" size="lg" className="drop-shadow-lg" />
		</div>
	),
};

export const ResponsiveUsage: Story = {
	render: () => (
		<div className="p-8">
			<RsdsLogo
				variant="color"
				className="h-auto w-12 sm:w-16 md:w-20 lg:w-24"
				alt="Responsive RS Design System RsdsLogo"
			/>
			<p className="mt-4 text-muted-foreground text-sm">
				This RsdsLogo uses responsive classes to scale with screen size
			</p>
		</div>
	),
};

export const ColorDebug: Story = {
	render: () => {
		// Create a test component with hardcoded colors for comparison
		const TestLogo = ({ useHardcoded = false }: { useHardcoded?: boolean; }) => {
			const colors = useHardcoded
				? { primary: "#FDD32F", secondary: "#E40135" }
				: { primary: "var(--color-ct-logo-secondar)", secondary: "var(--color-ct-logo-primary)" };

			return (
				<svg width="64" height="60" viewBox="0 0 64 60" fill="none" className="inline-block h-[7.5rem] w-8">
					<rect x="10" y="10" width="20" height="20" fill={colors.primary} />
					<rect x="35" y="10" width="20" height="20" fill={colors.secondary} />
					<text x="32" y="45" textAnchor="middle" fontSize="8" fill="currentColor">
						{useHardcoded ? "Hardcoded" : "CSS Vars"}
					</text>
				</svg>
			);
		};

		return (
			<div className="p-8 space-y-6">
				<h3 className="text-lg font-semibold">Color Debug Information</h3>

				<div className="grid grid-cols-3 gap-6">
					<div className="p-4 border rounded">
						<h4 className="font-medium mb-3">CSS Custom Properties Test</h4>
						<div className="space-y-2">
							<div className="flex items-center gap-2">
								<div
									className="w-6 h-6 border"
									style={{ backgroundColor: "var(--color-cp-yellow)" }}
								/>
								<span className="text-sm">--color-cp-yellow</span>
							</div>
							<div className="flex items-center gap-2">
								<div
									className="w-6 h-6 border"
									style={{ backgroundColor: "var(--color-cp-red)" }}
								/>
								<span className="text-sm">--color-cp-red</span>
							</div>
							<div className="flex items-center gap-2">
								<div
									className="w-6 h-6 border"
									style={{ backgroundColor: "var(--color-ct-logo-primary)" }}
								/>
								<span className="text-sm">--color-ct-logo-primary</span>
							</div>
							<div className="flex items-center gap-2">
								<div
									className="w-6 h-6 border"
									style={{ backgroundColor: "var(--color-ct-logo-secondar)" }}
								/>
								<span className="text-sm">--color-ct-logo-secondar</span>
							</div>
						</div>
					</div>

					<div className="p-4 border rounded">
						<h4 className="font-medium mb-3">Logo Comparison</h4>
						<div className="space-y-4">
							<div>
								<p className="text-sm mb-2">Hardcoded Colors:</p>
								<TestLogo useHardcoded={true} />
							</div>
							<div>
								<p className="text-sm mb-2">CSS Variables:</p>
								<TestLogo useHardcoded={false} />
							</div>
						</div>
					</div>

					<div className="p-4 border rounded">
						<h4 className="font-medium mb-3">Actual Logo Variants</h4>
						<div className="space-y-4">
							<div>
								<p className="text-sm mb-1">Color variant:</p>
								<RsdsLogo variant="color" size="sm" />
							</div>
							<div>
								<p className="text-sm mb-1">Black-gray variant:</p>
								<RsdsLogo variant="black-gray" size="sm" />
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	},
};
